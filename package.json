{"name": "asrrecognizeserver", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node server.js", "dev": "node server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.11.0", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^17.2.1", "express": "^5.1.0", "fs": "^0.0.1-security", "openai": "^5.10.2", "request": "^2.88.2", "stream": "^0.0.3", "uuid": "^11.1.0"}}