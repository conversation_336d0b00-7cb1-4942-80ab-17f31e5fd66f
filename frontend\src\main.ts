import './style.css'

// API基础URL - 使用相对路径，通过Nginx代理
const API_BASE_URL = '/api_compare/api'

// 接口类型定义
interface Language {
  code: string
  name: string
}

interface TTSResponse {
  success: boolean
  audioBase64?: string
  translatedText?: string
  message?: string
}

interface CompareResponse {
  success: boolean
  score?: number
  detail?: string
  message?: string
}

// DOM元素获取
const tabButtons = document.querySelectorAll('.tab-button')
const tabPanels = document.querySelectorAll('.tab-panel')
const languageSelect = document.querySelector('#language-select') as HTMLDivElement
const textInput = document.querySelector('#text-input') as HTMLTextAreaElement
const generateBtn = document.querySelector('#generate-btn') as HTMLButtonElement
const progressContainer = document.querySelector('#progress-container') as HTMLDivElement
const progressFill = document.querySelector('#progress-fill') as HTMLDivElement
const progressText = document.querySelector('#progress-text') as HTMLSpanElement
const audioResults = document.querySelector('#audio-results') as HTMLDivElement
const originalText = document.querySelector('#original-text') as HTMLTextAreaElement
const recognizedTextsList = document.querySelector('#recognized-texts-list') as HTMLDivElement
const addRecognizedTextBtn = document.querySelector('#add-recognized-text') as HTMLButtonElement
const compareBtn = document.querySelector('#compare-btn') as HTMLButtonElement

// 全局变量
let selectedLanguages: Language[] = []
let allLanguages: Language[] = []
let recognizedTextIndex = 0

// Tab切换功能
tabButtons.forEach(button => {
  button.addEventListener('click', () => {
    const tabId = button.getAttribute('data-tab')

    // 移除所有active类
    tabButtons.forEach(btn => btn.classList.remove('active'))
    tabPanels.forEach(panel => panel.classList.remove('active'))

    // 添加active类到当前选中的tab
    button.classList.add('active')
    document.querySelector(`#${tabId}-tab`)?.classList.add('active')
  })
})

// 加载语言列表
async function loadLanguages() {
  try {
    const response = await fetch(`${API_BASE_URL}/languages`)
    allLanguages = await response.json()

    // 初始化多选语言选择器
    initLanguageSelector()
    updateSelectedLanguagesDisplay()
  } catch (error) {
    console.error('加载语言列表失败:', error)
    const placeholder = languageSelect.querySelector('.placeholder')
    if (placeholder) {
      placeholder.textContent = '加载失败，请刷新重试'
    }
  }
}

// 初始化语言选择器
function initLanguageSelector() {
  const selectedLanguagesDiv = languageSelect.querySelector('.selected-languages') as HTMLDivElement
  const dropdown = document.querySelector('#language-dropdown') as HTMLDivElement

  // 清空下拉列表
  dropdown.innerHTML = ''

  // 添加语言选项
  allLanguages.forEach(lang => {
    const option = document.createElement('div')
    option.className = 'language-option'
    option.textContent = lang.name
    option.dataset.code = lang.code

    option.addEventListener('click', () => {
      toggleLanguageSelection(lang)
    })

    dropdown.appendChild(option)
  })

  // 点击选择器显示/隐藏下拉列表
  selectedLanguagesDiv.addEventListener('click', () => {
    const isVisible = dropdown.style.display === 'block'
    dropdown.style.display = isVisible ? 'none' : 'block'
    selectedLanguagesDiv.classList.toggle('active', !isVisible)
  })

  // 点击外部关闭下拉列表
  document.addEventListener('click', (e) => {
    if (!languageSelect.contains(e.target as Node)) {
      dropdown.style.display = 'none'
      selectedLanguagesDiv.classList.remove('active')
    }
  })
}

// 切换语言选择
function toggleLanguageSelection(language: Language) {
  const index = selectedLanguages.findIndex(lang => lang.code === language.code)

  if (index > -1) {
    // 移除语言
    selectedLanguages.splice(index, 1)
  } else {
    // 添加语言
    selectedLanguages.push(language)
  }

  updateSelectedLanguagesDisplay()
  updateLanguageDropdownState()
}

// 更新选中语言显示
function updateSelectedLanguagesDisplay() {
  const selectedLanguagesDiv = languageSelect.querySelector('.selected-languages') as HTMLDivElement

  if (selectedLanguages.length === 0) {
    selectedLanguagesDiv.innerHTML = '<span class="placeholder">请选择语言（可多选）</span>'
  } else {
    selectedLanguagesDiv.innerHTML = ''
    selectedLanguages.forEach(lang => {
      const tag = document.createElement('div')
      tag.className = 'language-tag'
      tag.innerHTML = `
        <span>${lang.name}</span>
        <span class="remove" data-code="${lang.code}">×</span>
      `

      // 添加移除事件
      const removeBtn = tag.querySelector('.remove') as HTMLSpanElement
      removeBtn.addEventListener('click', (e) => {
        e.stopPropagation()
        toggleLanguageSelection(lang)
      })

      selectedLanguagesDiv.appendChild(tag)
    })
  }
}

// 更新下拉列表状态
function updateLanguageDropdownState() {
  const dropdown = document.querySelector('#language-dropdown') as HTMLDivElement
  const options = dropdown.querySelectorAll('.language-option')

  options.forEach(option => {
    const htmlOption = option as HTMLDivElement
    const code = htmlOption.dataset.code
    const isSelected = selectedLanguages.some(lang => lang.code === code)
    option.classList.toggle('selected', isSelected)
  })
}

// 批量TTS生成功能
generateBtn.addEventListener('click', async () => {
  const text = textInput.value.trim()

  if (selectedLanguages.length === 0) {
    alert('请至少选择一种语言')
    return
  }

  if (!text) {
    alert('请输入中文文本')
    return
  }

  // 显示进度条
  progressContainer.style.display = 'block'
  audioResults.innerHTML = ''
  generateBtn.disabled = true

  const totalLanguages = selectedLanguages.length
  let completedCount = 0

  // 更新进度
  function updateProgress() {
    const percentage = Math.round((completedCount / totalLanguages) * 100)
    progressFill.style.width = `${percentage}%`
    progressText.textContent = `${percentage}%`
  }

  updateProgress()

  // 并发处理所有语言
  const promises = selectedLanguages.map(async (language) => {
    try {
      const response = await fetch(`${API_BASE_URL}/tts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          language: language.code,
          text: text,
          needTranslation: true
        })
      })

      const result: TTSResponse = await response.json()

      if (result.success && result.audioBase64) {
        // 创建音频结果项
        createAudioResultItem(language, result.translatedText || text, result.audioBase64)
      } else {
        createErrorResultItem(language, result.message || '生成失败')
      }
    } catch (error) {
      console.error(`${language.name} TTS生成失败:`, error)
      createErrorResultItem(language, '网络错误')
    } finally {
      completedCount++
      updateProgress()
    }
  })

  // 等待所有请求完成
  await Promise.all(promises)

  generateBtn.disabled = false

  // 3秒后隐藏进度条
  setTimeout(() => {
    progressContainer.style.display = 'none'
  }, 3000)
})

// 创建音频结果项
function createAudioResultItem(language: Language, translatedText: string, audioBase64: string) {
  const audioBlob = base64ToBlob(audioBase64, 'audio/mpeg')
  const audioUrl = URL.createObjectURL(audioBlob)

  const resultItem = document.createElement('div')
  resultItem.className = 'audio-result-item'
  resultItem.innerHTML = `
    <div class="audio-result-header">
      <div class="language-name">${language.name}</div>
    </div>
    <div class="audio-result-content">
      <div class="translated-text-section">
        <div class="label">翻译后的文本：</div>
        <div class="translated-text-content">
          ${translatedText}
          <button class="btn-copy" title="复制翻译文本">📋</button>
        </div>
      </div>
      <div class="audio-controls">
        <audio controls class="audio-player-small">
          <source src="${audioUrl}" type="audio/mpeg">
          您的浏览器不支持音频播放。
        </audio>
        <button class="btn btn-secondary download-btn">下载音频</button>
      </div>
    </div>
  `

  // 添加复制功能
  const copyBtn = resultItem.querySelector('.btn-copy') as HTMLButtonElement
  copyBtn.addEventListener('click', async () => {
    try {
      await navigator.clipboard.writeText(translatedText)
      const originalText = copyBtn.textContent
      copyBtn.textContent = '✅'
      setTimeout(() => {
        copyBtn.textContent = originalText
      }, 1000)
    } catch (error) {
      console.error('复制失败:', error)
      alert('复制失败，请手动选择文本复制')
    }
  })

  // 添加下载功能
  const downloadBtn = resultItem.querySelector('.download-btn') as HTMLButtonElement
  downloadBtn.addEventListener('click', () => {
    const link = document.createElement('a')
    link.href = audioUrl
    link.download = `tts_${language.code}_${Date.now()}.mp3`
    link.click()
  })

  audioResults.appendChild(resultItem)
}

// 创建错误结果项
function createErrorResultItem(language: Language, errorMessage: string) {
  const resultItem = document.createElement('div')
  resultItem.className = 'audio-result-item'
  resultItem.style.background = 'linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%)'
  resultItem.style.borderColor = '#f5c6cb'
  resultItem.innerHTML = `
    <div class="audio-result-header">
      <div class="language-name" style="color: #721c24;">${language.name}</div>
    </div>
    <div class="audio-result-content">
      <div style="color: #721c24; text-align: center; padding: 20px;">
        ❌ ${errorMessage}
      </div>
    </div>
  `

  audioResults.appendChild(resultItem)
}

// Base64转Blob
function base64ToBlob(base64: string, mimeType: string): Blob {
  const byteCharacters = atob(base64)
  const byteNumbers = new Array(byteCharacters.length)

  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i)
  }

  const byteArray = new Uint8Array(byteNumbers)
  return new Blob([byteArray], { type: mimeType })
}

// 添加语音识别文本输入框
addRecognizedTextBtn.addEventListener('click', () => {
  recognizedTextIndex++
  const newItem = createRecognizedTextItem(recognizedTextIndex)
  recognizedTextsList.appendChild(newItem)
})

// 创建语音识别文本输入项
function createRecognizedTextItem(index: number): HTMLDivElement {
  const item = document.createElement('div')
  item.className = 'recognized-text-item'
  item.dataset.index = index.toString()

  item.innerHTML = `
    <div class="text-input-section">
      ${index > 0 ? '<button class="btn-remove" title="删除此项">➖</button>' : ''}
      <textarea class="form-control recognized-text-input" rows="3" placeholder="请输入语音识别后的文本..."></textarea>
    </div>
    <div class="result-section">
      <span style="color: #6c757d;">等待比对...</span>
    </div>
  `

  // 添加删除功能（除了第一项）
  if (index > 0) {
    const removeBtn = item.querySelector('.btn-remove') as HTMLButtonElement
    removeBtn.addEventListener('click', () => {
      item.remove()
    })
  }

  return item
}

// 批量文本比对功能
compareBtn.addEventListener('click', async () => {
  const original = originalText.value.trim()
  const recognizedItems = recognizedTextsList.querySelectorAll('.recognized-text-item')

  if (!original) {
    alert('请输入原文')
    return
  }

  // 获取所有非空的语音识别文本
  const recognizedTexts: { element: HTMLDivElement, text: string, index: number }[] = []
  recognizedItems.forEach((item, index) => {
    const textarea = item.querySelector('.recognized-text-input') as HTMLTextAreaElement
    const text = textarea.value.trim()
    if (text) {
      recognizedTexts.push({
        element: item as HTMLDivElement,
        text: text,
        index: index
      })
    }
  })

  if (recognizedTexts.length === 0) {
    alert('请至少输入一条语音识别文本')
    return
  }

  compareBtn.disabled = true

  // 重置所有结果区域
  recognizedTexts.forEach(({ element }) => {
    const resultSection = element.querySelector('.result-section') as HTMLDivElement
    resultSection.className = 'result-section loading'
    resultSection.innerHTML = '<span style="color: #856404;">比对中...</span>'
  })

  // 并发处理所有比对
  const promises = recognizedTexts.map(async ({ element, text, index }) => {
    const resultSection = element.querySelector('.result-section') as HTMLDivElement

    try {
      const response = await fetch(`${API_BASE_URL}/compare`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ originalText: original, recognizedText: text })
      })

      const result: CompareResponse = await response.json()

      if (result.success && typeof result.score === 'number' && result.detail) {
        // 显示比对结果
        resultSection.className = 'result-section completed'

        let scoreClass = 'score-poor'
        if (result.score >= 90) {
          scoreClass = 'score-excellent'
        } else if (result.score >= 70) {
          scoreClass = 'score-good'
        } else if (result.score >= 30) {
          scoreClass = 'score-fair'
        }

        resultSection.innerHTML = `
          <div class="compare-result-inline">
            <div class="score-display ${scoreClass}">${result.score}</div>
            <div class="detail-display">${result.detail}</div>
          </div>
        `
      } else {
        resultSection.className = 'result-section'
        resultSection.innerHTML = `<span style="color: #dc3545;">❌ ${result.message || '比对失败'}</span>`
      }
    } catch (error) {
      console.error(`文本比对失败 (${index}):`, error)
      resultSection.className = 'result-section'
      resultSection.innerHTML = '<span style="color: #dc3545;">❌ 网络错误</span>'
    }
  })

  // 等待所有比对完成
  await Promise.all(promises)

  compareBtn.disabled = false
})

// 页面初始化
function initApp() {
  loadLanguages()

  // 初始化第一个语音识别文本输入框
  const firstItem = createRecognizedTextItem(0)
  recognizedTextsList.appendChild(firstItem)
}

// 确保DOM加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initApp)
} else {
  initApp()
}
