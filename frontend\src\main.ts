import './style.css'

// API基础URL
const API_BASE_URL = 'http://localhost:5578/api'

// 接口类型定义
interface Language {
  code: string
  name: string
}

interface TTSResponse {
  success: boolean
  audioBase64?: string
  translatedText?: string
  message?: string
}

interface CompareResponse {
  success: boolean
  score?: number
  detail?: string
  message?: string
}

// DOM元素获取
const tabButtons = document.querySelectorAll('.tab-button')
const tabPanels = document.querySelectorAll('.tab-panel')
const languageSelect = document.querySelector('#language-select') as HTMLSelectElement
const textInput = document.querySelector('#text-input') as HTMLTextAreaElement
const generateBtn = document.querySelector('#generate-btn') as HTMLButtonElement
const audioResult = document.querySelector('#audio-result') as HTMLDivElement
const audioPlayer = document.querySelector('#audio-player') as HTMLAudioElement
const downloadBtn = document.querySelector('#download-btn') as HTMLButtonElement
const translatedTextContainer = document.querySelector('#translated-text-container') as HTMLDivElement
const translatedText = document.querySelector('#translated-text') as HTMLDivElement
const copyTranslatedBtn = document.querySelector('#copy-translated-btn') as HTMLButtonElement
const originalText = document.querySelector('#original-text') as HTMLTextAreaElement
const recognizedText = document.querySelector('#recognized-text') as HTMLTextAreaElement
const compareBtn = document.querySelector('#compare-btn') as HTMLButtonElement
const compareResult = document.querySelector('#compare-result') as HTMLDivElement
const scoreValue = document.querySelector('#score-value') as HTMLDivElement
const detailText = document.querySelector('#detail-text') as HTMLDivElement

// Tab切换功能
tabButtons.forEach(button => {
  button.addEventListener('click', () => {
    const tabId = button.getAttribute('data-tab')

    // 移除所有active类
    tabButtons.forEach(btn => btn.classList.remove('active'))
    tabPanels.forEach(panel => panel.classList.remove('active'))

    // 添加active类到当前选中的tab
    button.classList.add('active')
    document.querySelector(`#${tabId}-tab`)?.classList.add('active')
  })
})

// 加载语言列表
async function loadLanguages() {
  try {
    const response = await fetch(`${API_BASE_URL}/languages`)
    const languages: Language[] = await response.json()

    languageSelect.innerHTML = '<option value="">请选择语言</option>'
    languages.forEach(lang => {
      const option = document.createElement('option')
      option.value = lang.code
      option.textContent = lang.name
      languageSelect.appendChild(option)
    })
  } catch (error) {
    console.error('加载语言列表失败:', error)
    languageSelect.innerHTML = '<option value="">加载失败，请刷新重试</option>'
  }
}

// 设置按钮加载状态
function setButtonLoading(button: HTMLButtonElement, loading: boolean) {
  const btnText = button.querySelector('.btn-text') as HTMLSpanElement
  const loadingSpinner = button.querySelector('.loading-spinner') as HTMLSpanElement

  if (loading) {
    btnText.style.display = 'none'
    loadingSpinner.style.display = 'inline-block'
    button.disabled = true
  } else {
    btnText.style.display = 'inline-block'
    loadingSpinner.style.display = 'none'
    button.disabled = false
  }
}

// TTS生成功能
generateBtn.addEventListener('click', async () => {
  const language = languageSelect.value
  const text = textInput.value.trim()
  const ttsMode = document.querySelector('input[name="tts-mode"]:checked') as HTMLInputElement

  if (!language) {
    alert('请选择语言')
    return
  }

  if (!text) {
    alert('请输入要转换的文本')
    return
  }

  setButtonLoading(generateBtn, true)
  audioResult.style.display = 'none'
  translatedTextContainer.style.display = 'none'

  try {
    const requestBody: any = { language, text }

    // 如果选择了翻译模式，添加翻译标志
    if (ttsMode.value === 'translate') {
      requestBody.needTranslation = true
    }

    const response = await fetch(`${API_BASE_URL}/tts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    })

    const result: TTSResponse = await response.json()

    if (result.success && result.audioBase64) {
      // 创建音频blob
      const audioBlob = base64ToBlob(result.audioBase64, 'audio/mpeg')
      const audioUrl = URL.createObjectURL(audioBlob)

      // 设置音频播放器
      audioPlayer.src = audioUrl

      // 如果有翻译文本，显示翻译结果
      if (result.translatedText) {
        translatedText.textContent = result.translatedText
        translatedTextContainer.style.display = 'block'
      }

      // 设置下载功能
      downloadBtn.onclick = () => {
        const link = document.createElement('a')
        link.href = audioUrl
        link.download = `tts_${Date.now()}.mp3`
        link.click()
      }

      audioResult.style.display = 'block'
    } else {
      alert(result.message || '生成失败，请重试')
    }
  } catch (error) {
    console.error('TTS生成失败:', error)
    alert('生成失败，请检查网络连接')
  } finally {
    setButtonLoading(generateBtn, false)
  }
})

// Base64转Blob
function base64ToBlob(base64: string, mimeType: string): Blob {
  const byteCharacters = atob(base64)
  const byteNumbers = new Array(byteCharacters.length)

  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i)
  }

  const byteArray = new Uint8Array(byteNumbers)
  return new Blob([byteArray], { type: mimeType })
}

// 文本比对功能
compareBtn.addEventListener('click', async () => {
  const original = originalText.value.trim()
  const recognized = recognizedText.value.trim()

  if (!original) {
    alert('请输入原文')
    return
  }

  if (!recognized) {
    alert('请输入语音识别文本')
    return
  }

  setButtonLoading(compareBtn, true)
  compareResult.style.display = 'none'

  try {
    const response = await fetch(`${API_BASE_URL}/compare`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ originalText: original, recognizedText: recognized })
    })

    const result: CompareResponse = await response.json()

    if (result.success && typeof result.score === 'number' && result.detail) {
      // 显示评分
      scoreValue.textContent = result.score.toString()

      // 根据评分设置颜色
      scoreValue.className = 'score-value'
      if (result.score >= 90) {
        scoreValue.classList.add('score-excellent')
      } else if (result.score >= 70) {
        scoreValue.classList.add('score-good')
      } else if (result.score >= 30) {
        scoreValue.classList.add('score-fair')
      } else {
        scoreValue.classList.add('score-poor')
      }

      // 显示详情
      detailText.textContent = result.detail

      compareResult.style.display = 'block'
    } else {
      alert(result.message || '比对失败，请重试')
    }
  } catch (error) {
    console.error('文本比对失败:', error)
    alert('比对失败，请检查网络连接')
  } finally {
    setButtonLoading(compareBtn, false)
  }
})

// 复制翻译文本功能
copyTranslatedBtn.addEventListener('click', async () => {
  const textToCopy = translatedText.textContent
  if (textToCopy) {
    try {
      await navigator.clipboard.writeText(textToCopy)
      // 临时改变按钮文本显示复制成功
      const originalText = copyTranslatedBtn.textContent
      copyTranslatedBtn.textContent = '✅'
      setTimeout(() => {
        copyTranslatedBtn.textContent = originalText
      }, 1000)
    } catch (error) {
      console.error('复制失败:', error)
      alert('复制失败，请手动选择文本复制')
    }
  }
})

// 页面初始化
function initApp() {
  loadLanguages()
}

// 确保DOM加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initApp)
} else {
  initApp()
}
