#!/bin/bash

# 部署脚本
echo "开始部署语音生成与文本比对平台..."

# 检查Node.js版本
echo "检查Node.js版本..."
node --version
npm --version

# 安装后端依赖
echo "安装后端依赖..."
npm install --production

# 构建前端
echo "构建前端..."
cd frontend
npm install
npm run build
cd ..

# 创建日志目录
echo "创建日志目录..."
mkdir -p logs

# 停止现有服务
echo "停止现有服务..."
pm2 stop tts-server 2>/dev/null || true

# 启动服务
echo "启动后端服务..."
pm2 start ecosystem.config.js

# 启动前端开发服务器（生产环境建议使用构建后的静态文件）
echo "启动前端服务..."
cd frontend
pm2 start npm --name "tts-frontend" -- run dev
cd ..

# 显示服务状态
echo "服务状态："
pm2 status

echo "部署完成！"
echo "后端服务运行在端口 5578"
echo "前端服务运行在端口 5173"
echo "请配置Nginx代理到HTTPS"
