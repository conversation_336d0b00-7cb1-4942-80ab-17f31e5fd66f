:root {
  font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  font-weight: 400;
  color: #333;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.header {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  padding: 30px;
  text-align: center;
}

.header h1 {
  font-size: 2.5rem;
  font-weight: 600;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tabs {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.tab-button {
  flex: 1;
  padding: 20px;
  border: none;
  background: transparent;
  font-size: 1.1rem;
  font-weight: 500;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.tab-button:hover {
  background: rgba(79, 172, 254, 0.1);
  color: #4facfe;
}

.tab-button.active {
  color: #4facfe;
  background: white;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.tab-content {
  padding: 40px;
}

.tab-panel {
  display: none;
}

.tab-panel.active {
  display: block;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.form-group {
  margin-bottom: 25px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #495057;
  font-size: 1rem;
}

.form-control {
  width: 100%;
  padding: 15px;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.form-control:focus {
  outline: none;
  border-color: #4facfe;
  box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
}

.form-control:hover {
  border-color: #ced4da;
}

textarea.form-control {
  resize: vertical;
  min-height: 120px;
}

.btn {
  padding: 15px 30px;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn-primary {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
}

.btn-secondary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.loading-spinner {
  display: inline-block;
}

.audio-result {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  border: 1px solid #c3e6cb;
  border-radius: 12px;
  padding: 25px;
  margin-top: 20px;
  text-align: center;
}

.success-message {
  color: #155724;
  font-weight: 600;
  font-size: 1.1rem;
  margin-bottom: 15px;
}

.audio-player {
  width: 100%;
  max-width: 400px;
  margin: 15px 0;
  border-radius: 8px;
}

.radio-group {
  display: flex;
  gap: 20px;
  margin-top: 8px;
}

.radio-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 10px 15px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: white;
}

.radio-option:hover {
  border-color: #4facfe;
  background: rgba(79, 172, 254, 0.05);
}

.radio-option input[type="radio"] {
  margin: 0;
  cursor: pointer;
}

.radio-option input[type="radio"]:checked + .radio-text {
  color: #4facfe;
  font-weight: 600;
}

.radio-option:has(input[type="radio"]:checked) {
  border-color: #4facfe;
  background: rgba(79, 172, 254, 0.1);
}

.radio-text {
  font-size: 0.95rem;
  color: #495057;
  transition: all 0.3s ease;
}

.translated-text-container {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 1px solid #ffeaa7;
  border-radius: 12px;
  padding: 20px;
  margin: 15px 0;
}

.translated-label {
  font-weight: 600;
  color: #856404;
  margin-bottom: 10px;
  font-size: 1rem;
}

.translated-text-box {
  position: relative;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
  min-height: 60px;
}

.translated-text {
  line-height: 1.6;
  color: #495057;
  word-wrap: break-word;
  padding-right: 40px;
}

.btn-copy {
  position: absolute;
  top: 10px;
  right: 10px;
  background: transparent;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.btn-copy:hover {
  background: rgba(79, 172, 254, 0.1);
  transform: scale(1.1);
}

.compare-container {
  max-width: 800px;
  margin: 0 auto;
}

.text-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 30px;
}

@media (max-width: 768px) {
  .text-group {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

.compare-result {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border: 1px solid #bbdefb;
  border-radius: 12px;
  padding: 25px;
  margin-top: 20px;
}

.score-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  gap: 15px;
}

.score-label {
  font-weight: 600;
  font-size: 1.2rem;
  color: #495057;
}

.score-value {
  font-size: 2rem;
  font-weight: 700;
  padding: 10px 20px;
  border-radius: 50px;
  min-width: 80px;
  text-align: center;
}

.score-excellent {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
}

.score-good {
  background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
  color: white;
}

.score-fair {
  background: linear-gradient(135deg, #fd7e14 0%, #dc3545 100%);
  color: white;
}

.score-poor {
  background: linear-gradient(135deg, #dc3545 0%, #6f42c1 100%);
  color: white;
}

.detail-container {
  text-align: left;
}

.detail-label {
  font-weight: 600;
  font-size: 1.1rem;
  color: #495057;
  margin-bottom: 10px;
}

.detail-text {
  background: white;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  line-height: 1.6;
  color: #495057;
}

/* 多选语言选择器 */
.language-multi-select {
  position: relative;
}

.selected-languages {
  min-height: 50px;
  padding: 15px;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.selected-languages:hover {
  border-color: #4facfe;
}

.selected-languages.active {
  border-color: #4facfe;
  box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
}

.placeholder {
  color: #6c757d;
  font-style: italic;
}

.language-tag {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 6px;
}

.language-tag .remove {
  cursor: pointer;
  font-weight: bold;
  opacity: 0.8;
}

.language-tag .remove:hover {
  opacity: 1;
}

.language-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 2px solid #4facfe;
  border-radius: 12px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.language-option {
  padding: 12px 15px;
  cursor: pointer;
  transition: background 0.2s ease;
  border-bottom: 1px solid #f8f9fa;
}

.language-option:hover {
  background: rgba(79, 172, 254, 0.1);
}

.language-option.selected {
  background: rgba(79, 172, 254, 0.2);
  color: #4facfe;
  font-weight: 600;
}

.language-option:last-child {
  border-bottom: none;
}

/* 进度条样式 */
.progress-container {
  margin: 20px 0;
}

.progress-label {
  font-weight: 600;
  margin-bottom: 10px;
  color: #495057;
}

.progress-bar {
  width: 100%;
  height: 20px;
  background: #e9ecef;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  width: 0%;
  transition: width 0.3s ease;
  border-radius: 10px;
}

/* 音频结果列表 */
.audio-results {
  margin-top: 20px;
}

.audio-result-item {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  border: 1px solid #c3e6cb;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 15px;
}

.audio-result-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 15px;
}

.language-name {
  font-weight: 600;
  color: #155724;
  font-size: 1.1rem;
}

.audio-result-content {
  display: grid;
  gap: 15px;
}

.translated-text-section {
  background: rgba(255, 255, 255, 0.8);
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.translated-text-section .label {
  font-weight: 600;
  color: #856404;
  margin-bottom: 8px;
}

.translated-text-content {
  position: relative;
  background: white;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.audio-controls {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.audio-player-small {
  flex: 1;
  min-width: 200px;
}

/* 文本比对新样式 */
.recognized-texts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.btn-add {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-add:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.recognized-texts-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.recognized-text-item {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  padding: 20px;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  background: white;
  position: relative;
}

.text-input-section {
  position: relative;
}

.btn-remove {
  position: absolute;
  top: -10px;
  right: -10px;
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.btn-remove:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.result-section {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100px;
  border: 2px dashed #e9ecef;
  border-radius: 8px;
  background: #f8f9fa;
}

.result-section.loading {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border-color: #ffeaa7;
}

.result-section.completed {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border: 1px solid #bbdefb;
  padding: 15px;
}

.compare-result-inline {
  width: 100%;
  text-align: center;
}

.score-display {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 10px;
  padding: 10px 20px;
  border-radius: 50px;
  display: inline-block;
  min-width: 80px;
}

.detail-display {
  background: white;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  line-height: 1.6;
  color: #495057;
  text-align: left;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    margin: 10px;
    border-radius: 15px;
  }

  .header {
    padding: 20px;
  }

  .header h1 {
    font-size: 2rem;
  }

  .tab-content {
    padding: 20px;
  }

  .score-container {
    flex-direction: column;
    gap: 10px;
  }

  .recognized-text-item {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .audio-controls {
    flex-direction: column;
    align-items: stretch;
  }
}
