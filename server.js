const express = require('express')
const cors = require('cors')
const { getAllLanguages, getLanguageProvider, getAliLanguageCode, isLanguageSupported } = require('./languages')
const { getValidToken } = require('./tokenManager')
const { synthesizeTextAli, aliVoices } = require('./tts_ali')
const { generateTTS } = require('./tts_openai')
const { chatCompletion } = require('./llm')

const app = express()
const PORT = 3000

// 中间件
app.use(cors())
app.use(express.json())

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error('服务器错误:', err)
  res.status(500).json({
    success: false,
    message: '服务器内部错误'
  })
})

// 获取语言列表接口
app.get('/api/languages', (req, res) => {
  try {
    const languages = getAllLanguages()
    res.json(languages)
  } catch (error) {
    console.error('获取语言列表失败:', error)
    res.status(500).json({
      success: false,
      message: '获取语言列表失败'
    })
  }
})

// 翻译接口
app.post('/api/translate', async (req, res) => {
  try {
    const { text, targetLanguage } = req.body

    // 参数验证
    if (!text || !targetLanguage) {
      return res.status(400).json({
        success: false,
        message: '文本和目标语言参数不能为空'
      })
    }

    // 构建翻译提示词
    const languageNames = {
      'zh_hans': '简体中文',
      'zh_hant': '繁体中文',
      'en': '英语',
      'ja': '日语',
      'ko': '韩语',
      'de': '德语',
      'es': '西班牙语',
      'ru': '俄语',
      'fr': '法语',
      'it': '意大利语',
      'pt': '葡萄牙语',
      'ar': '阿拉伯语',
      'hi': '印地语',
      'th': '泰语',
      'vi': '越南语',
      'tr': '土耳其语',
      'pl': '波兰语',
      'nl': '荷兰语',
      'sv': '瑞典语',
      'da': '丹麦语',
      'no': '挪威语',
      'fi': '芬兰语'
    }

    const targetLanguageName = languageNames[targetLanguage] || targetLanguage

    const prompt = `请将以下中文文本翻译成${targetLanguageName}，只返回翻译结果，不要包含其他内容：

${text}`

    // 调用LLM进行翻译
    const translatedText = await chatCompletion(prompt)

    res.json({
      success: true,
      translatedText: translatedText.trim()
    })

  } catch (error) {
    console.error('翻译失败:', error)
    res.status(500).json({
      success: false,
      message: '翻译失败: ' + error.message
    })
  }
})

// TTS生成接口
app.post('/api/tts', async (req, res) => {
  try {
    const { language, text, needTranslation } = req.body

    // 参数验证
    if (!language || !text) {
      return res.status(400).json({
        success: false,
        message: '语言和文本参数不能为空'
      })
    }

    // 检查语言是否支持
    if (!isLanguageSupported(language)) {
      return res.status(400).json({
        success: false,
        message: '不支持的语言'
      })
    }

    let finalText = text
    let translatedText = null

    // 如果需要翻译，先进行翻译
    if (needTranslation) {
      try {
        const translateResponse = await fetch('http://localhost:3000/api/translate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            text: text,
            targetLanguage: language
          })
        })

        const translateResult = await translateResponse.json()
        if (translateResult.success) {
          finalText = translateResult.translatedText
          translatedText = translateResult.translatedText
        } else {
          throw new Error('翻译失败')
        }
      } catch (translateError) {
        console.error('翻译过程出错:', translateError)
        return res.status(500).json({
          success: false,
          message: '翻译失败，请重试'
        })
      }
    }

    const provider = getLanguageProvider(language)
    let audioBase64

    if (provider === 'ali') {
      // 使用阿里云TTS
      const aliLangCode = getAliLanguageCode(language)
      const token = await getValidToken()

      // 获取对应语言的默认音色
      let voice
      if (aliVoices[aliLangCode] && aliVoices[aliLangCode].default) {
        voice = aliVoices[aliLangCode].default
      } else {
        voice = 'xiaoyun' // 默认音色
      }

      audioBase64 = await synthesizeTextAli({
        text: finalText,
        voice,
        token,
        languageCode: aliLangCode
      })
    } else if (provider === 'openai') {
      // 使用OpenAI TTS
      audioBase64 = await generateTTS(finalText)
    } else {
      return res.status(400).json({
        success: false,
        message: '未知的TTS提供商'
      })
    }

    const response = {
      success: true,
      audioBase64
    }

    // 如果进行了翻译，返回翻译后的文本
    if (translatedText) {
      response.translatedText = translatedText
    }

    res.json(response)

  } catch (error) {
    console.error('TTS生成失败:', error)
    res.status(500).json({
      success: false,
      message: 'TTS生成失败: ' + error.message
    })
  }
})

// 文本比对接口
app.post('/api/compare', async (req, res) => {
  try {
    const { originalText, recognizedText } = req.body
    
    // 参数验证
    if (!originalText || !recognizedText) {
      return res.status(400).json({
        success: false,
        message: '原文和语音识别文本不能为空'
      })
    }
    
    // 构建提示词
    const prompt = `你是一个专业的文本比对评估专家。请比较以下两段文本：

原文：
${originalText}

语音识别文本：
${recognizedText}

请根据以下标准进行评分：
- 90分以上：很完美，基本上跟原文差别不大
- 70分以上：及格，虽然跟原文有出入但是不影响理解
- 30分以上：不及格，已经影响到理解了
- 30分以下：完全牛头不对马嘴

请返回JSON格式的结果，包含score（数字）和detail（字符串）两个字段。
score是0-100的整数评分，detail是详细的比对分析说明。

示例格式：
{"score": 88, "detail": "相比原文，语音识别后的文本在一些意思描述有点不清晰，但总体来说是正确的"}

请直接返回JSON，不要包含其他内容：`
    
    // 调用LLM进行比对
    const llmResponse = await chatCompletion(prompt)
    
    // 解析LLM返回的JSON
    let result
    try {
      // 尝试提取JSON部分
      const jsonMatch = llmResponse.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        result = JSON.parse(jsonMatch[0])
      } else {
        throw new Error('无法从LLM响应中提取JSON')
      }
    } catch (parseError) {
      console.error('解析LLM响应失败:', parseError)
      console.error('LLM原始响应:', llmResponse)
      
      // 如果解析失败，返回默认结果
      return res.status(500).json({
        success: false,
        message: '文本比对结果解析失败'
      })
    }
    
    // 验证结果格式
    if (typeof result.score !== 'number' || typeof result.detail !== 'string') {
      return res.status(500).json({
        success: false,
        message: '文本比对结果格式错误'
      })
    }
    
    res.json({
      success: true,
      score: result.score,
      detail: result.detail
    })
    
  } catch (error) {
    console.error('文本比对失败:', error)
    res.status(500).json({
      success: false,
      message: '文本比对失败: ' + error.message
    })
  }
})

// 健康检查接口
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: '服务器运行正常',
    timestamp: new Date().toISOString()
  })
})

// 启动服务器
app.listen(PORT, () => {
  console.log(`服务器已启动，端口: ${PORT}`)
  console.log(`API地址: http://localhost:${PORT}/api`)
  console.log('支持的接口:')
  console.log('  GET  /api/languages - 获取语言列表')
  console.log('  POST /api/translate - 文本翻译')
  console.log('  POST /api/tts - TTS生成（支持翻译模式）')
  console.log('  POST /api/compare - 文本比对')
  console.log('  GET  /api/health - 健康检查')
})

module.exports = app
