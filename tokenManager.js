const fs = require('fs')
const path = require('path')
const { getToken } = require('./genAliToken')

// Token存储文件路径
const TOKEN_FILE_PATH = path.join(__dirname, 'ali_token.json')

// 读取本地token
function readLocalToken() {
  try {
    if (fs.existsSync(TOKEN_FILE_PATH)) {
      const data = fs.readFileSync(TOKEN_FILE_PATH, 'utf8')
      return JSON.parse(data)
    }
  } catch (error) {
    console.error('读取本地token失败:', error)
  }
  return null
}

// 保存token到本地
function saveLocalToken(tokenData) {
  try {
    fs.writeFileSync(TOKEN_FILE_PATH, JSON.stringify(tokenData, null, 2))
    console.log('Token已保存到本地')
  } catch (error) {
    console.error('保存token失败:', error)
  }
}

// 检查token是否过期
function isTokenExpired(tokenData) {
  if (!tokenData || !tokenData.expireTime) {
    return true
  }
  
  const currentTime = Math.floor(Date.now() / 1000)
  // 提前5分钟刷新token
  const bufferTime = 5 * 60
  
  return currentTime >= (tokenData.expireTime - bufferTime)
}

// 获取有效的token
async function getValidToken() {
  try {
    // 读取本地token
    let localToken = readLocalToken()
    
    // 检查token是否过期
    if (!localToken || isTokenExpired(localToken)) {
      console.log('Token已过期或不存在，正在获取新token...')
      
      // 获取新token
      const newToken = await getToken()
      if (newToken) {
        saveLocalToken(newToken)
        localToken = newToken
        console.log('新token获取成功')
      } else {
        throw new Error('获取新token失败')
      }
    } else {
      console.log('使用本地有效token')
    }
    
    return localToken.token
  } catch (error) {
    console.error('获取有效token失败:', error)
    throw error
  }
}

module.exports = {
  getValidToken,
  readLocalToken,
  saveLocalToken,
  isTokenExpired
}
