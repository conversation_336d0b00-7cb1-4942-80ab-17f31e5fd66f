const { synthesizeTextAli, aliVoices } = require('./tts_ali')
const { generateTTS } = require('./tts_openai')
const { chatCompletion } = require('./llm')
const fs = require('fs')

async function main() {
    chatCompletion("你是谁").then(res => {
        console.log(res) 
    })
}

function saveAudioToFile(audioBuffer, outputPath) {
    const audioData = Buffer.from(audioBuffer, 'base64')
    fs.writeFileSync(outputPath, audioData)
    console.log(`Audio saved to ${outputPath}`)
}

main()
