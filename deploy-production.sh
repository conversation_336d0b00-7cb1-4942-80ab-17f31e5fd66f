#!/bin/bash

# 生产环境部署脚本
echo "开始生产环境部署..."

# 检查Node.js版本
echo "检查Node.js版本..."
node --version
npm --version

# 安装后端依赖
echo "安装后端依赖..."
npm install --production

# 构建前端静态文件
echo "构建前端静态文件..."
cd frontend
npm install
npm run build
cd ..

# 创建日志目录
echo "创建日志目录..."
mkdir -p logs

# 停止现有服务
echo "停止现有服务..."
pm2 stop tts-server 2>/dev/null || true
pm2 stop tts-frontend 2>/dev/null || true

# 启动后端服务
echo "启动后端服务..."
pm2 start ecosystem.config.js

# 复制构建后的静态文件到Nginx目录（可选）
echo "前端静态文件已构建到 frontend/dist 目录"
echo "如需使用静态文件部署，请将 frontend/dist 复制到Nginx配置的静态文件目录"

# 显示服务状态
echo "服务状态："
pm2 status

echo "生产环境部署完成！"
echo "后端服务运行在端口 5578"
echo "前端静态文件位于 frontend/dist"
echo "访问地址: https://iyuuki.xyz/asr_compare/"
