# 语音生成与文本比对平台

这是一个集成了TTS（文本转语音）和文本比对功能的Web应用平台。

## 功能特性

### 🎵 语音生成功能
- **多语言批量生成**: 支持50多种语言同时选择
- **智能翻译模式**: 输入中文自动翻译为目标语言
- **实时进度监控**: 批量生成时显示进度条和完成状态
- **结果列表展示**: 每种语言独立显示翻译文本和音频
- **智能TTS分配**: 自动选择最佳提供商（阿里云TTS + OpenAI TTS）
- **便捷操作**: 翻译文本一键复制，音频在线试听和下载
- **现代化UI**: 响应式设计，支持移动端

### 📝 文本比对功能
- **批量比对模式**: 支持一对多文本比对
- **动态输入管理**: 可添加/删除多条语音识别文本
- **内联结果显示**: 每条文本旁边直接显示比对结果
- **AI智能分析**: 大语言模型驱动的文本相似度分析
- **智能评分系统**: 0-100分评分，颜色编码显示
- **详细分析报告**: 提供具体的差异分析说明

## 技术架构

### 前端
- **框架**: Vite + TypeScript
- **样式**: 现代化CSS3，渐变背景，响应式设计
- **功能**: Tab切换，异步API调用，音频播放

### 后端
- **框架**: Node.js + Express
- **TTS服务**: 阿里云TTS + OpenAI TTS
- **AI分析**: 大语言模型文本比对
- **API**: RESTful接口设计

## 快速开始

### 环境要求
- Node.js 16+
- npm 或 yarn

### 安装依赖
```bash
# 安装后端依赖
npm install

# 安装前端依赖
cd frontend
npm install
```

### 配置环境变量
创建 `.env` 文件并配置：
```env
OPENAI_API_KEY=your_openai_api_key
```

### 启动服务

1. **启动后端服务器**
```bash
npm start
# 服务器将在 http://localhost:5578 启动
```

2. **启动前端开发服务器**
```bash
cd frontend
npm run dev
# 前端将在 http://localhost:5173 启动
```

### 访问应用
- 开发环境: http://localhost:5173
- 生产环境: https://your-domain.com (通过Nginx代理)

## 🚀 生产环境部署

### Ubuntu服务器部署
详细部署指南请查看 [DEPLOYMENT.md](./DEPLOYMENT.md)

#### 快速部署
```bash
# 1. 上传项目到服务器
# 2. 配置环境变量
echo "OPENAI_API_KEY=your_key" > .env

# 3. 运行部署脚本
chmod +x deploy.sh
./deploy.sh

# 4. 配置Nginx (参考nginx.conf)
sudo cp nginx.conf /etc/nginx/sites-available/tts-platform
sudo ln -s /etc/nginx/sites-available/tts-platform /etc/nginx/sites-enabled/
sudo nginx -t && sudo systemctl reload nginx
```

#### 服务端口
- 后端API: 5578
- 前端开发: 5173
- HTTPS: 443 (Nginx代理)

## API接口

### 获取语言列表
```http
GET /api/languages
```

### 文本翻译
```http
POST /api/translate
Content-Type: application/json

{
  "text": "你好世界",
  "targetLanguage": "en"
}
```

### TTS生成
```http
POST /api/tts
Content-Type: application/json

# 直接模式
{
  "language": "zh_hans",
  "text": "你好世界"
}

# 翻译模式
{
  "language": "en",
  "text": "你好世界",
  "needTranslation": true
}
```

### 文本比对
```http
POST /api/compare
Content-Type: application/json

{
  "originalText": "原始文本",
  "recognizedText": "识别文本"
}
```

### 健康检查
```http
GET /api/health
```

## 支持的语言

平台支持50多种语言，包括：
- 简体中文、繁体中文
- 英语、日语、韩语
- 德语、法语、西班牙语、意大利语
- 俄语、阿拉伯语、印地语
- 以及更多其他语言...

## 评分标准

文本比对评分标准：
- **90-100分**: 🟢 优秀 - 与原文几乎完全一致
- **70-89分**: 🟡 良好 - 有小差异但不影响理解
- **30-69分**: 🟠 一般 - 有明显差异，影响理解
- **0-29分**: 🔴 较差 - 差异很大，严重影响理解

## 项目结构

```
├── server.js              # 主服务器文件
├── languages.js           # 语言配置
├── tokenManager.js        # Token管理
├── genAliToken.js         # 阿里云Token生成
├── tts_ali.js            # 阿里云TTS
├── tts_openai.js         # OpenAI TTS
├── llm.js                # 大语言模型
├── frontend/             # 前端项目
│   ├── src/
│   │   ├── main.ts       # 主逻辑
│   │   └── style.css     # 样式文件
│   └── index.html        # 主页面
└── README.md             # 项目说明
```

## 开发说明

### 测试API
运行API测试脚本：
```bash
node test_api.js
```

### 构建前端
```bash
cd frontend
npm run build
```

## 注意事项

1. 确保阿里云AccessKey配置正确
2. 确保OpenAI API Key有效
3. 首次使用时会自动获取阿里云Token
4. Token会自动管理和刷新

## 许可证

MIT License
