你需要写一个前端加一个后端。前端用于用户交互，后端用于处理前端的接口，并与tts和llm平台交互。前端UI设计的尽量现代化点，尽量好看美观！

下面是你的详细任务：

1. 你需要基于vite框架写一个前端，这个前端主要用于：
    1. 有两个大的Tab标签可切换页面，一个Tab叫：语音生成，一个Tab叫：文本比对。
    2. TTS生成功能：可选择语言，然后输入文本。然后点击生成按钮后往服务器发送请求并显示loading状态，服务器返回生成成功后提示”生成语音成功”。用户可以点击试听，以及下载（跟浏览器自带的mp3控件一样）。
        1. 可选择的语言列表是直接从服务器拉取的，包含语言code和语言显示的文本，下拉栏直接显示文本就行，code是留着发给服务器的
        2. 服务器将直接返回base64流，拿到后将流转为mp3存到浏览器缓存中，并展示给用户。
        3. mp3的名称就叫 “tts_{time}.mp3”即可
    3. 文本比对功能：给出两个多行文本框，左边文本框标题叫“原文”，右边文本框叫“语音识别文本”，两边都输入后点击”比对”按钮，会往服务器发送请求，此时显示loading状态。服务器返回比对结果后将结果显示出来
        1. 服务器返回的结果，除开接口成功与否外，包含2个参数：score, detail。
        2. score:代表此次比对的评分。90以上文本要显示绿色，70以上显示黄色，30以上显示橙色，30以下显示红色。
        3. detail:代表此次比对的详情总结。直接显示文本即可
        4. 举例：{”score”: 88, “detail”: “相比原文，语音识别后的文本在一些意思描述有点不清晰，但总体来说是正确的”}
2. 你需要基于nodejs写一个后端，这个后端主要用于：
    1. 提供一个语言列表接口，用于前端展示哪些语言可选择
        1. 返回语言code和语言展示的文本列表即可
    2. 提供一个tts接口。这个接口接收语言和文本两个参数。接收到后去调用tts平台接口生成tts，拿到Tts的结果后转为base64并返还给前端
        1. 我会在后文提供给你语言列表以及tts的本地接口，你组合调用就行了
    3. 提供一个文本比对接口。这个接口接收原文和语音识别文本两个参数。接收到后去调用llm接口并写入提示词，从大模型返回的结果中提取出score和detail，然后返回给前端
        1. 你总结下提示词，主要就是让大模型接收一个原文和语音识别后的文本，然后让他做比对并打分。告诉他90分以上是很完美，基本上跟原文差别不大；70分以上是及格，虽然跟原文有出入但是不影响理解；30分以上是不及格，已经影响到理解了；30分以下是完全牛头不对马嘴。
        2. 让大模型返回json结果，解析后返回score和detail给前端即可
3. 前端你自己写就行，我不用提供什么。后端的话，接下来我会给你提供一些用于生成tts以及调用llm的js文件。
    1. 阿里tts
        1. 包含两个文件：genAliToken.js和tts_ali.js
        2. genAliToken提供getToken的Promise方法，调用后返回如下示例的结果
            
            ```jsx
            {
              token: '9115ea0eb1954b3290697525592cbdd6',
              expireTime: 1753972142,
              expireDate: '2025/7/31 22:29:02'
            }
            ```
            
        3. tts_ali.js提供synthesizeTextAli方法以及aliVoices列表，你主要需要传入文本、音色以及token
            
            ```jsx
            async function main() {
                synthesizeTextAli(
                    {
                        text: "你好呀你是谁",
                        voice: aliVoices.zh.default,
                        token: localToken
                    }
                ).then(res => {
                    // base64音频
                    saveAudioToFile(res, "test.mp3")
                })
            }
            
            function saveAudioToFile(audioBuffer, outputPath) {
                const audioData = Buffer.from(audioBuffer, 'base64')
                fs.writeFileSync(outputPath, audioData)
                console.log(`Audio saved to ${outputPath}`)
            }
            ```
            
        4. 总结阿里tts流程就是：
            1. 你自己维护一份本地json文件。每次准备调用alitts之前，先去json文件查一下当前时间是否大于expireData的时间，如果大于的话就刷新下token再调用alitts，如果还没过期就直接调用alitts
            2. 每次调用时，voice传该语言列表下的default即可
    2. openaiTTS
        1. 对于阿里模型不支持的语言，你需要切换到使用openaiTTS来生成TTS。
        2. tts_openai.js中提供了generateTTS方法，跟阿里一样，返回base64
            
            ```jsx
            async function main() {
                generateTTS("你好呀你是谁")
                    .then(res => {
                       // base64音频
                       saveAudioToFile(res, "test.mp3")
                    })
            }
            
            function saveAudioToFile(audioBuffer, outputPath) {
                const audioData = Buffer.from(audioBuffer, 'base64')
                fs.writeFileSync(outputPath, audioData)
                console.log(`Audio saved to ${outputPath}`)
            }
            
            ```
            
    3. llm
        1. 对于文本比对功能，你需要用到llm.js中的chatCompletion方法
            
            ```jsx
                chatCompletion("你是谁").then(res => {
                    console.log(res) 
                })
            ```
            
        2. 你需要自行想一下文本比对的提示词设计，然后输入chatCompletion即可
    4. 语言列表
        1. 后台中需要支持的语言列表如下：
            
            简体中文、英语、日语、韩语、德语、西班牙语、俄罗斯语、繁体中文、泰语、越南语、法语、意大利语、菲律宾语、印尼语、土耳其语、波兰语、阿拉伯语、马来语、葡萄牙语、印地语、波斯语、乌克兰语、希腊语、荷兰语、希伯来语、匈牙利语、罗马尼亚语、捷克语、泰米尔语、瑞典语、孟加拉语、保加利亚语、挪威语、丹麦语、高棉语、尼泊尔语、僧伽罗语、斯瓦希里语、缅甸语、老挝语、乌兹别克语、哈萨克语、阿塞拜疆语、豪撒语、加泰罗尼亚语、克罗地亚语、蒙古语、爪哇语、维语、藏语
            
        2. 每个语言的code你自己总结下就行，按照国际的惯例来定。比如简体中文就zh_hans，日语就ja之类的。反正这个不会展示给用户，是你自己定义起来用于前后端交互。