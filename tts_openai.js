// tts-api.js
require('dotenv').config();               // 加载 .env 中的 OPENAI_API_KEY
const { default: OpenAI } = require('openai');

const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY
});

/**
 * 生成 TTS 并返回 Base64 编码
 * @param {string} text     - 要合成的文本
 * @returns {Promise<string>} Promise resolves to Base64 字符串
 */
async function generateTTS(text) {
    if (!text) {
        throw new Error('参数 language 和 text 都不能为空');
    }
    const resp = await openai.audio.speech.create({
        model: 'gpt-4o-mini-tts',
        voice: "ash",
        input: text,
        response_format: 'mp3'
    });
    const buffer = Buffer.from(await resp.arrayBuffer());
    return buffer.toString('base64');
}

module.exports = { generateTTS };
