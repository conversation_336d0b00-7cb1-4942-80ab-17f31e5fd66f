<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>语音生成与文本比对平台</title>
  </head>
  <body>
    <div id="app">
      <div class="container">
        <header class="header">
          <h1>语音生成与文本比对平台</h1>
        </header>

        <div class="tabs">
          <button class="tab-button active" data-tab="tts">语音生成</button>
          <button class="tab-button" data-tab="compare">文本比对</button>
        </div>

        <div class="tab-content">
          <!-- TTS Tab -->
          <div id="tts-tab" class="tab-panel active">
            <div class="form-group">
              <label for="language-select">选择语言：</label>
              <select id="language-select" class="form-control">
                <option value="">加载中...</option>
              </select>
            </div>

            <div class="form-group">
              <label>生成模式：</label>
              <div class="radio-group">
                <label class="radio-option">
                  <input type="radio" name="tts-mode" value="direct" checked>
                  <span class="radio-text">原文直接生成语音</span>
                </label>
                <label class="radio-option">
                  <input type="radio" name="tts-mode" value="translate">
                  <span class="radio-text">由中文翻译后再生成语音</span>
                </label>
              </div>
            </div>

            <div class="form-group">
              <label for="text-input">输入文本：</label>
              <textarea id="text-input" class="form-control" rows="4" placeholder="请输入要转换为语音的文本..."></textarea>
            </div>

            <div class="form-group">
              <button id="generate-btn" class="btn btn-primary">
                <span class="btn-text">生成语音</span>
                <span class="loading-spinner" style="display: none;">生成中...</span>
              </button>
            </div>

            <div id="audio-result" class="audio-result" style="display: none;">
              <div class="success-message">✅ 生成语音成功</div>

              <div id="translated-text-container" class="translated-text-container" style="display: none;">
                <div class="translated-label">翻译后的文本：</div>
                <div class="translated-text-box">
                  <div id="translated-text" class="translated-text"></div>
                  <button id="copy-translated-btn" class="btn-copy" title="复制翻译文本">📋</button>
                </div>
              </div>

              <audio id="audio-player" controls class="audio-player">
                您的浏览器不支持音频播放。
              </audio>
              <button id="download-btn" class="btn btn-secondary">下载音频</button>
            </div>
          </div>

          <!-- Compare Tab -->
          <div id="compare-tab" class="tab-panel">
            <div class="compare-container">
              <div class="text-group">
                <div class="form-group">
                  <label for="original-text">原文：</label>
                  <textarea id="original-text" class="form-control" rows="6" placeholder="请输入原文..."></textarea>
                </div>

                <div class="form-group">
                  <label for="recognized-text">语音识别文本：</label>
                  <textarea id="recognized-text" class="form-control" rows="6" placeholder="请输入语音识别后的文本..."></textarea>
                </div>
              </div>

              <div class="form-group">
                <button id="compare-btn" class="btn btn-primary">
                  <span class="btn-text">比对</span>
                  <span class="loading-spinner" style="display: none;">比对中...</span>
                </button>
              </div>

              <div id="compare-result" class="compare-result" style="display: none;">
                <div class="score-container">
                  <div class="score-label">评分：</div>
                  <div id="score-value" class="score-value">0</div>
                </div>
                <div class="detail-container">
                  <div class="detail-label">详情：</div>
                  <div id="detail-text" class="detail-text"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
