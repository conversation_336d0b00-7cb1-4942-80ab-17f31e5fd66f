(function(){const n=document.createElement("link").relList;if(n&&n.supports&&n.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))e(s);new MutationObserver(s=>{for(const o of s)if(o.type==="childList")for(const a of o.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&e(a)}).observe(document,{childList:!0,subtree:!0});function r(s){const o={};return s.integrity&&(o.integrity=s.integrity),s.referrerPolicy&&(o.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?o.credentials="include":s.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function e(s){if(s.ep)return;s.ep=!0;const o=r(s);fetch(s.href,o)}})();const f="http://localhost:5578/api",g=document.querySelectorAll(".tab-button"),T=document.querySelectorAll(".tab-panel"),i=document.querySelector("#language-select"),v=document.querySelector("#text-input"),y=document.querySelector("#generate-btn"),b=document.querySelector("#audio-result"),q=document.querySelector("#audio-player"),w=document.querySelector("#download-btn"),S=document.querySelector("#translated-text-container"),L=document.querySelector("#translated-text"),l=document.querySelector("#copy-translated-btn"),C=document.querySelector("#original-text"),B=document.querySelector("#recognized-text"),p=document.querySelector("#compare-btn"),h=document.querySelector("#compare-result"),c=document.querySelector("#score-value"),E=document.querySelector("#detail-text");g.forEach(t=>{t.addEventListener("click",()=>{const n=t.getAttribute("data-tab");g.forEach(r=>r.classList.remove("active")),T.forEach(r=>r.classList.remove("active")),t.classList.add("active"),document.querySelector(`#${n}-tab`)?.classList.add("active")})});async function O(){try{const n=await(await fetch(`${f}/languages`)).json();i.innerHTML='<option value="">请选择语言</option>',n.forEach(r=>{const e=document.createElement("option");e.value=r.code,e.textContent=r.name,i.appendChild(e)})}catch(t){console.error("加载语言列表失败:",t),i.innerHTML='<option value="">加载失败，请刷新重试</option>'}}function d(t,n){const r=t.querySelector(".btn-text"),e=t.querySelector(".loading-spinner");n?(r.style.display="none",e.style.display="inline-block",t.disabled=!0):(r.style.display="inline-block",e.style.display="none",t.disabled=!1)}y.addEventListener("click",async()=>{const t=i.value,n=v.value.trim(),r=document.querySelector('input[name="tts-mode"]:checked');if(!t){alert("请选择语言");return}if(!n){alert("请输入要转换的文本");return}d(y,!0),b.style.display="none",S.style.display="none";try{const e={language:t,text:n};r.value==="translate"&&(e.needTranslation=!0);const o=await(await fetch(`${f}/tts`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).json();if(o.success&&o.audioBase64){const a=A(o.audioBase64,"audio/mpeg"),m=URL.createObjectURL(a);q.src=m,o.translatedText&&(L.textContent=o.translatedText,S.style.display="block"),w.onclick=()=>{const u=document.createElement("a");u.href=m,u.download=`tts_${Date.now()}.mp3`,u.click()},b.style.display="block"}else alert(o.message||"生成失败，请重试")}catch(e){console.error("TTS生成失败:",e),alert("生成失败，请检查网络连接")}finally{d(y,!1)}});function A(t,n){const r=atob(t),e=new Array(r.length);for(let o=0;o<r.length;o++)e[o]=r.charCodeAt(o);const s=new Uint8Array(e);return new Blob([s],{type:n})}p.addEventListener("click",async()=>{const t=C.value.trim(),n=B.value.trim();if(!t){alert("请输入原文");return}if(!n){alert("请输入语音识别文本");return}d(p,!0),h.style.display="none";try{const e=await(await fetch(`${f}/compare`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({originalText:t,recognizedText:n})})).json();e.success&&typeof e.score=="number"&&e.detail?(c.textContent=e.score.toString(),c.className="score-value",e.score>=90?c.classList.add("score-excellent"):e.score>=70?c.classList.add("score-good"):e.score>=30?c.classList.add("score-fair"):c.classList.add("score-poor"),E.textContent=e.detail,h.style.display="block"):alert(e.message||"比对失败，请重试")}catch(r){console.error("文本比对失败:",r),alert("比对失败，请检查网络连接")}finally{d(p,!1)}});l.addEventListener("click",async()=>{const t=L.textContent;if(t)try{await navigator.clipboard.writeText(t);const n=l.textContent;l.textContent="✅",setTimeout(()=>{l.textContent=n},1e3)}catch(n){console.error("复制失败:",n),alert("复制失败，请手动选择文本复制")}});function x(){O()}document.readyState==="loading"?document.addEventListener("DOMContentLoaded",x):x();
