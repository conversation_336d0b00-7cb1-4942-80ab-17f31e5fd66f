# Ubuntu服务器部署指南

## 🚀 完整部署流程

### 1. 服务器环境准备

#### 更新系统
```bash
sudo apt update && sudo apt upgrade -y
```

#### 安装Node.js (推荐使用NodeSource)
```bash
# 安装Node.js 18.x LTS
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version
```

#### 安装PM2进程管理器
```bash
sudo npm install -g pm2
```

#### 安装Nginx
```bash
sudo apt install nginx -y
sudo systemctl enable nginx
sudo systemctl start nginx
```

### 2. 项目部署

#### 上传项目文件
```bash
# 方法1: 使用git clone
git clone <your-repository-url>
cd ASRRecognizeServer

# 方法2: 使用scp上传
scp -r /local/path/ASRRecognizeServer user@server:/home/<USER>/
```

#### 配置环境变量
```bash
# 创建.env文件
nano .env

# 添加以下内容：
OPENAI_API_KEY=your_openai_api_key_here
```

#### 运行部署脚本
```bash
chmod +x deploy.sh
./deploy.sh
```

### 3. Nginx配置

#### 复制配置文件
```bash
sudo cp nginx.conf /etc/nginx/sites-available/tts-platform
sudo ln -s /etc/nginx/sites-available/tts-platform /etc/nginx/sites-enabled/
```

#### 修改配置
```bash
sudo nano /etc/nginx/sites-available/tts-platform

# 修改以下内容：
# 1. 替换 your-domain.com 为您的实际域名
# 2. 配置SSL证书路径
```

#### 测试并重启Nginx
```bash
sudo nginx -t
sudo systemctl reload nginx
```

### 4. SSL证书配置

#### 使用Let's Encrypt (推荐)
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx -y

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加：0 12 * * * /usr/bin/certbot renew --quiet
```

#### 手动SSL证书
如果您有自己的SSL证书，请将证书文件上传到服务器并在nginx.conf中配置正确的路径。

### 5. 防火墙配置

```bash
# 开放必要端口
sudo ufw allow 22      # SSH
sudo ufw allow 80      # HTTP
sudo ufw allow 443     # HTTPS
sudo ufw allow 5578    # 后端API (可选，如果需要直接访问)
sudo ufw allow 5173    # 前端开发服务器 (可选)

# 启用防火墙
sudo ufw enable
```

### 6. 服务管理

#### PM2常用命令
```bash
# 查看服务状态
pm2 status

# 查看日志
pm2 logs tts-server
pm2 logs tts-frontend

# 重启服务
pm2 restart tts-server
pm2 restart tts-frontend

# 停止服务
pm2 stop tts-server
pm2 stop tts-frontend

# 开机自启
pm2 startup
pm2 save
```

#### 系统服务状态
```bash
# 检查Nginx状态
sudo systemctl status nginx

# 重启Nginx
sudo systemctl restart nginx
```

### 7. 监控和维护

#### 查看服务日志
```bash
# PM2日志
pm2 logs

# Nginx日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# 系统日志
sudo journalctl -u nginx -f
```

#### 性能监控
```bash
# 安装htop
sudo apt install htop

# 监控系统资源
htop

# 监控PM2进程
pm2 monit
```

### 8. 故障排除

#### 常见问题

1. **端口被占用**
```bash
sudo netstat -tulpn | grep :5578
sudo netstat -tulpn | grep :5173
```

2. **权限问题**
```bash
sudo chown -R $USER:$USER /path/to/project
chmod +x deploy.sh
```

3. **Node.js模块问题**
```bash
rm -rf node_modules package-lock.json
npm install
```

4. **Nginx配置错误**
```bash
sudo nginx -t
sudo systemctl status nginx
```

### 9. 生产环境优化

#### 前端构建部署 (推荐)
```bash
# 构建前端静态文件
cd frontend
npm run build

# 配置Nginx直接服务静态文件
sudo nano /etc/nginx/sites-available/tts-platform
# 修改root路径指向dist目录
```

#### 数据库配置 (如需要)
如果后续需要添加数据库，推荐使用：
- PostgreSQL 或 MySQL
- Redis (缓存)

### 10. 备份策略

#### 自动备份脚本
```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf /backup/tts-platform-$DATE.tar.gz /path/to/project
# 保留最近7天的备份
find /backup -name "tts-platform-*.tar.gz" -mtime +7 -delete
```

#### 定时备份
```bash
sudo crontab -e
# 添加：0 2 * * * /path/to/backup.sh
```

## 🎯 部署检查清单

- [ ] Node.js 18+ 已安装
- [ ] PM2 已安装
- [ ] Nginx 已安装并配置
- [ ] 环境变量已配置 (.env文件)
- [ ] 项目依赖已安装
- [ ] 后端服务运行在5578端口
- [ ] 前端服务运行在5173端口
- [ ] SSL证书已配置
- [ ] 防火墙已配置
- [ ] 域名DNS已解析
- [ ] 服务自启动已配置

## 📞 技术支持

如果在部署过程中遇到问题，请检查：
1. 服务器日志文件
2. PM2进程状态
3. Nginx配置语法
4. 防火墙设置
5. 域名解析

部署完成后，您可以通过以下地址访问应用：
- 开发环境: http://localhost:5173
- 生产环境: https://iyuuki.xyz/asr_compare/
