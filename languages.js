// 支持的语言列表配置
// 根据阿里云TTS和OpenAI TTS的支持情况进行分配

const languages = [
  // 阿里云TTS支持的语言
  { code: 'zh_hans', name: '简体中文', provider: 'ali' },
  { code: 'en', name: '英语', provider: 'ali' },
  { code: 'ja', name: '日语', provider: 'ali' },
  { code: 'ko', name: '韩语', provider: 'ali' },
  { code: 'de', name: '德语', provider: 'ali' },
  { code: 'es', name: '西班牙语', provider: 'ali' },
  { code: 'ru', name: '俄罗斯语', provider: 'ali' },
  { code: 'th', name: '泰语', provider: 'ali' },
  { code: 'vi', name: '越南语', provider: 'ali' },
  { code: 'fr', name: '法语', provider: 'ali' },
  { code: 'it', name: '意大利语', provider: 'ali' },
  { code: 'fil', name: '菲律宾语', provider: 'ali' },
  { code: 'id', name: '印尼语', provider: 'ali' },
  { code: 'pt', name: '葡萄牙语', provider: 'ali' }, // 阿里云用英语代替
  
  // OpenAI TTS支持的语言（阿里云不支持的）
  { code: 'zh_hant', name: '繁体中文', provider: 'openai' },
  { code: 'tr', name: '土耳其语', provider: 'openai' },
  { code: 'pl', name: '波兰语', provider: 'openai' },
  { code: 'ar', name: '阿拉伯语', provider: 'openai' },
  { code: 'ms', name: '马来语', provider: 'openai' },
  { code: 'hi', name: '印地语', provider: 'openai' },
  { code: 'fa', name: '波斯语', provider: 'openai' },
  { code: 'uk', name: '乌克兰语', provider: 'openai' },
  { code: 'el', name: '希腊语', provider: 'openai' },
  { code: 'nl', name: '荷兰语', provider: 'openai' },
  { code: 'he', name: '希伯来语', provider: 'openai' },
  { code: 'hu', name: '匈牙利语', provider: 'openai' },
  { code: 'ro', name: '罗马尼亚语', provider: 'openai' },
  { code: 'cs', name: '捷克语', provider: 'openai' },
  { code: 'ta', name: '泰米尔语', provider: 'openai' },
  { code: 'sv', name: '瑞典语', provider: 'openai' },
  { code: 'bn', name: '孟加拉语', provider: 'openai' },
  { code: 'bg', name: '保加利亚语', provider: 'openai' },
  { code: 'no', name: '挪威语', provider: 'openai' },
  { code: 'da', name: '丹麦语', provider: 'openai' },
  { code: 'km', name: '高棉语', provider: 'openai' },
  { code: 'ne', name: '尼泊尔语', provider: 'openai' },
  { code: 'si', name: '僧伽罗语', provider: 'openai' },
  { code: 'sw', name: '斯瓦希里语', provider: 'openai' },
  { code: 'my', name: '缅甸语', provider: 'openai' },
  { code: 'lo', name: '老挝语', provider: 'openai' },
  { code: 'uz', name: '乌兹别克语', provider: 'openai' },
  { code: 'kk', name: '哈萨克语', provider: 'openai' },
  { code: 'az', name: '阿塞拜疆语', provider: 'openai' },
  { code: 'ha', name: '豪撒语', provider: 'openai' },
  { code: 'ca', name: '加泰罗尼亚语', provider: 'openai' },
  { code: 'hr', name: '克罗地亚语', provider: 'openai' },
  { code: 'mn', name: '蒙古语', provider: 'openai' },
  { code: 'jv', name: '爪哇语', provider: 'openai' },
  { code: 'ug', name: '维语', provider: 'openai' },
  { code: 'bo', name: '藏语', provider: 'openai' }
]

// 阿里云语言代码映射
const aliLanguageMap = {
  'zh_hans': 'zh',
  'en': 'en',
  'ja': 'ja',
  'ko': 'ko',
  'de': 'de',
  'es': 'es',
  'ru': 'ru',
  'th': 'th',
  'vi': 'vi',
  'fr': 'fr',
  'it': 'it',
  'fil': 'fil',
  'id': 'id',
  'pt': 'en' // 阿里云不支持葡萄牙语，用英语代替
}

// 获取所有语言列表
function getAllLanguages() {
  return languages.map(lang => ({
    code: lang.code,
    name: lang.name
  }))
}

// 根据语言代码获取提供商
function getLanguageProvider(languageCode) {
  const language = languages.find(lang => lang.code === languageCode)
  return language ? language.provider : null
}

// 获取阿里云语言代码
function getAliLanguageCode(languageCode) {
  return aliLanguageMap[languageCode] || null
}

// 检查语言是否支持
function isLanguageSupported(languageCode) {
  return languages.some(lang => lang.code === languageCode)
}

module.exports = {
  getAllLanguages,
  getLanguageProvider,
  getAliLanguageCode,
  isLanguageSupported
}
