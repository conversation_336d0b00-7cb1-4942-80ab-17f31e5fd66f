const OpenAI = require('openai')
const client = new OpenAI({
    apiKey: '3c4635c7-281b-4a28-951d-0b674d7ad600',
    baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
})

async function chatCompletion(text = "你好") {
    try {
        const messages = [{
            role: 'user',
            content: `${text}`
        }]

        console.log(messages)
        const stream = await client.chat.completions.create(
            {
                model: 'ep-20250616165918-xvmhl',
                messages: [{
                    role: 'user',
                    content: `${text}`
                }],
            }
        )
        return stream.choices[0].message.content
    } catch (error) {
        const rJson = {
            source_text: message,
            target_text: '',
            finish_reason: 'STOP',
            status: 'error',
            message: error.message,
        }
        callback(rJson)
    }
}

module.exports = {
    chatCompletion
}